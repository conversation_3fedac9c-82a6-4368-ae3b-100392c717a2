import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import TerminalWindow from "./TerminalWindow";
import PricingTierSelector from "./PricingTierSelector";
import TicketCategorySelector from "./TicketCategorySelector";
import FileUpload from "./FileUpload";
import SystemInfoCollector from "./SystemInfoCollector";
import { useAutoSave } from "@/hooks/useAutoSave";
import {
  validateTicketForm,
  getFieldError,
  hasFieldError,
  getCharacterCount,
  getWordCount,
  isFieldRequired,
  TicketFormData,
} from "@/lib/validation";
import {
  Save,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Tag,
  Phone,
  Mail,
  Calendar,
} from "lucide-react";

interface CreateTicketFormProps {
  onTicketCreated: () => void;
}

interface TicketCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  default_priority: string;
  estimated_resolution_hours: number;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

const CreateTicketForm: React.FC<CreateTicketFormProps> = ({
  onTicketCreated,
}) => {
  // Form state
  const [formData, setFormData] = useState<Partial<TicketFormData>>({
    title: "",
    description: "",
    priority: "medium",
    contact_method: "email",
    preferred_contact_time: "",
    tags: [],
    urgency_level: 1,
  });

  const [selectedCategory, setSelectedCategory] =
    useState<TicketCategory | null>(null);
  const [pricingTier, setPricingTier] = useState("standard");
  const [attachments, setAttachments] = useState<UploadedFile[]>([]);
  const [systemInfo, setSystemInfo] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<
    string,
    string
  > | null>(null);
  const [showDraftSaved, setShowDraftSaved] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();

  // Auto-save functionality
  const { saveDraft, loadDraft, clearDraft, lastSaved, isSaving } = useAutoSave(
    {
      ...formData,
      category_id: selectedCategory?.id,
      system_info: systemInfo,
    },
    {
      enabled: true,
      onSave: (success) => {
        if (success) {
          setShowDraftSaved(true);
          setTimeout(() => setShowDraftSaved(false), 3000);
        }
      },
      onLoad: (draft) => {
        if (draft) {
          setFormData({
            title: draft.title || "",
            description: draft.description || "",
            priority: draft.priority || "medium",
            contact_method: draft.contact_method || "email",
            preferred_contact_time: draft.preferred_contact_time || "",
            tags: draft.tags || [],
            urgency_level: draft.urgency_level || 1,
          });
          if (draft.category_id) {
            // Load category details if needed
            loadCategoryById(draft.category_id);
          }
          if (draft.system_info) {
            setSystemInfo(draft.system_info);
          }
        }
      },
    }
  );

  const loadCategoryById = async (categoryId: string) => {
    try {
      const { data, error } = await supabase
        .from("ticket_categories")
        .select("*")
        .eq("id", categoryId)
        .single();

      if (!error && data) {
        setSelectedCategory(data);
      }
    } catch (error) {
      console.error("Error loading category:", error);
    }
  };

  const getPriceForTier = (tier: string) => {
    const prices = {
      standard: "$29.99",
      priority: "$49.99",
      urgent: "$99.99",
    };
    return prices[tier] || "$29.99";
  };

  // Form validation
  const validateForm = useCallback(() => {
    const dataToValidate = {
      ...formData,
      category_id: selectedCategory?.id,
      system_info: systemInfo,
    };

    const validation = validateTicketForm(dataToValidate);
    setValidationErrors(validation.errors);
    return validation.success;
  }, [formData, selectedCategory, systemInfo]);

  // Form field handlers
  const updateFormField = (field: keyof TicketFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev) => {
        if (!prev) return null;
        const { [field]: removed, ...rest } = prev;
        return Object.keys(rest).length > 0 ? rest : null;
      });
    }
  };

  const handleCategorySelect = (category: TicketCategory | null) => {
    setSelectedCategory(category);
    if (category) {
      // Auto-set priority based on category default
      updateFormField("priority", category.default_priority as any);
    }
  };

  const handleSystemInfoChange = (info: any) => {
    setSystemInfo(info);
  };

  const handleFilesUploaded = (files: UploadedFile[]) => {
    setAttachments(files);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    // Validate form before submission
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Prepare ticket data
      const ticketData = {
        user_id: user.id,
        title: formData.title!,
        description: formData.description!,
        priority: formData.priority!,
        category_id: selectedCategory?.id,
        urgency_level: formData.urgency_level,
        tags: formData.tags,
        system_info: systemInfo,
        contact_method: formData.contact_method,
        preferred_contact_time: formData.preferred_contact_time,
        status: "pending_payment",
        payment_status: "pending",
      };

      const { data: ticket, error: ticketError } = await supabase
        .from("tickets")
        .insert(ticketData)
        .select()
        .single();

      if (ticketError) throw ticketError;

      // Save attachments to the ticket
      if (attachments.length > 0) {
        const attachmentPromises = attachments.map(async (file) => {
          return supabase.from("ticket_attachments").insert({
            ticket_id: ticket.id,
            file_name: file.name,
            file_size: file.size,
            file_type: file.type,
            file_url: file.url,
            storage_path: file.url, // Assuming URL contains the path
            uploaded_by: user.id,
          });
        });

        await Promise.all(attachmentPromises);
      }

      // Initiate payment
      const { data: paymentData, error: paymentError } =
        await supabase.functions.invoke("create-ticket-payment", {
          body: { ticketId: ticket.id, pricingTier },
        });

      if (paymentError) throw paymentError;

      // Clear draft after successful submission
      await clearDraft();

      // Redirect to Stripe Checkout
      if (paymentData?.url) {
        window.open(paymentData.url, "_blank");
      }

      toast({
        title: "Ticket Created!",
        description: "Please complete the payment to submit your ticket.",
      });

      // Reset form
      setFormData({
        title: "",
        description: "",
        priority: "medium",
        contact_method: "email",
        preferred_contact_time: "",
        tags: [],
        urgency_level: 1,
      });
      setSelectedCategory(null);
      setAttachments([]);
      setSystemInfo(null);
      setCurrentStep(1);
      setValidationErrors(null);

      onTicketCreated();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create ticket",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Progress calculation
  const calculateProgress = () => {
    let completed = 0;
    const total = 5; // Total required steps

    if (selectedCategory) completed++;
    if (formData.title && formData.title.length >= 5) completed++;
    if (formData.description && formData.description.length >= 20) completed++;
    if (formData.priority) completed++;
    if (systemInfo) completed++;

    return (completed / total) * 100;
  };

  const getStepStatus = (step: number) => {
    switch (step) {
      case 1:
        return selectedCategory
          ? "completed"
          : currentStep === 1
          ? "current"
          : "pending";
      case 2:
        return formData.title && formData.title.length >= 5
          ? "completed"
          : currentStep === 2
          ? "current"
          : "pending";
      case 3:
        return formData.description && formData.description.length >= 20
          ? "completed"
          : currentStep === 3
          ? "current"
          : "pending";
      case 4:
        return attachments.length > 0 || currentStep >= 4
          ? "completed"
          : currentStep === 4
          ? "current"
          : "pending";
      case 5:
        return systemInfo
          ? "completed"
          : currentStep === 5
          ? "current"
          : "pending";
      default:
        return "pending";
    }
  };

  return (
    <TerminalWindow title="CREATE_TICKET.EXE">
      <div className="space-y-6">
        {/* Header with Progress */}
        <div className="text-center mb-6">
          <div className="text-lg font-bold text-retro-green mb-2">
            &gt; NEW SUPPORT REQUEST
          </div>
          <div className="text-sm opacity-80 mb-4">
            Enhanced ticket submission system with smart features
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-retro-green/60">
              <span>Progress</span>
              <span>{Math.round(calculateProgress())}% Complete</span>
            </div>
            <Progress value={calculateProgress()} className="h-2" />
          </div>

          {/* Auto-save indicator */}
          {showDraftSaved && (
            <div className="flex items-center justify-center mt-2 text-xs text-retro-green">
              <Save className="w-3 h-3 mr-1" />
              Draft saved automatically
            </div>
          )}
          {isSaving && (
            <div className="flex items-center justify-center mt-2 text-xs text-retro-green/60">
              <Clock className="w-3 h-3 mr-1 animate-spin" />
              Saving draft...
            </div>
          )}
        </div>

        {/* Step 1: Category Selection */}
        <div className="space-y-4">
          <TicketCategorySelector
            selectedCategory={selectedCategory?.id || null}
            onCategorySelect={handleCategorySelect}
          />
        </div>

        {/* Pricing Tier Selection */}
        {selectedCategory && (
          <div className="space-y-4">
            <PricingTierSelector
              selectedTier={pricingTier}
              onTierSelect={setPricingTier}
            />
          </div>
        )}

        {/* Main Form */}
        {selectedCategory && (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Step 2: Title */}
            <Card className="border-retro-green/50 bg-black">
              <CardHeader>
                <CardTitle className="text-retro-green font-mono flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Ticket Details
                  {isFieldRequired("title") && (
                    <span className="text-red-400 ml-1">*</span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-retro-green">TICKET TITLE:</Label>
                  <Input
                    value={formData.title || ""}
                    onChange={(e) => updateFormField("title", e.target.value)}
                    className={`bg-black border-retro-green text-retro-green focus:border-retro-green ${
                      hasFieldError(validationErrors, "title")
                        ? "border-red-400"
                        : ""
                    }`}
                    placeholder="Brief, descriptive title for your issue..."
                    maxLength={200}
                  />
                  <div className="flex justify-between text-xs">
                    <span className="text-red-400">
                      {getFieldError(validationErrors, "title")}
                    </span>
                    <span className="text-retro-green/60">
                      {getCharacterCount(formData.title || "")}/200 characters
                    </span>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label className="text-retro-green">ISSUE DESCRIPTION:</Label>
                  <Textarea
                    value={formData.description || ""}
                    onChange={(e) =>
                      updateFormField("description", e.target.value)
                    }
                    className={`bg-black border-retro-green text-retro-green focus:border-retro-green min-h-[120px] ${
                      hasFieldError(validationErrors, "description")
                        ? "border-red-400"
                        : ""
                    }`}
                    placeholder="Detailed description of your issue, steps to reproduce, error messages, etc..."
                    maxLength={5000}
                  />
                  <div className="flex justify-between text-xs">
                    <span className="text-red-400">
                      {getFieldError(validationErrors, "description")}
                    </span>
                    <span className="text-retro-green/60">
                      {getCharacterCount(formData.description || "")}/5000
                      characters ({getWordCount(formData.description || "")}{" "}
                      words)
                    </span>
                  </div>
                </div>

                {/* Priority */}
                <div className="space-y-2">
                  <Label className="text-retro-green">PRIORITY LEVEL:</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(
                      value: "low" | "medium" | "high" | "urgent"
                    ) => updateFormField("priority", value)}
                  >
                    <SelectTrigger className="bg-black border-retro-green text-retro-green">
                      <SelectValue placeholder="Select priority level" />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-retro-green">
                      <SelectItem
                        value="low"
                        className="text-retro-green hover:bg-retro-green/20"
                      >
                        LOW - General questions
                      </SelectItem>
                      <SelectItem
                        value="medium"
                        className="text-retro-green hover:bg-retro-green/20"
                      >
                        MEDIUM - Standard issues
                      </SelectItem>
                      <SelectItem
                        value="high"
                        className="text-retro-green hover:bg-retro-green/20"
                      >
                        HIGH - Important problems
                      </SelectItem>
                      <SelectItem
                        value="urgent"
                        className="text-retro-green hover:bg-retro-green/20"
                      >
                        URGENT - Critical issues
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Contact Preferences */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-retro-green">
                      PREFERRED CONTACT:
                    </Label>
                    <Select
                      value={formData.contact_method}
                      onValueChange={(value: "email" | "phone" | "both") =>
                        updateFormField("contact_method", value)
                      }
                    >
                      <SelectTrigger className="bg-black border-retro-green text-retro-green">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-black border-retro-green">
                        <SelectItem
                          value="email"
                          className="text-retro-green hover:bg-retro-green/20"
                        >
                          <div className="flex items-center">
                            <Mail className="w-4 h-4 mr-2" />
                            Email Only
                          </div>
                        </SelectItem>
                        <SelectItem
                          value="phone"
                          className="text-retro-green hover:bg-retro-green/20"
                        >
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 mr-2" />
                            Phone Only
                          </div>
                        </SelectItem>
                        <SelectItem
                          value="both"
                          className="text-retro-green hover:bg-retro-green/20"
                        >
                          <div className="flex items-center">
                            <Mail className="w-4 h-4 mr-1" />
                            <Phone className="w-4 h-4 mr-1" />
                            Both
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-retro-green">PREFERRED TIME:</Label>
                    <Input
                      value={formData.preferred_contact_time || ""}
                      onChange={(e) =>
                        updateFormField(
                          "preferred_contact_time",
                          e.target.value
                        )
                      }
                      className="bg-black border-retro-green text-retro-green focus:border-retro-green"
                      placeholder="e.g., 9 AM - 5 PM EST"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Step 3: File Attachments */}
            <Card className="border-retro-green/50 bg-black">
              <CardHeader>
                <CardTitle className="text-retro-green font-mono flex items-center">
                  <Tag className="w-5 h-5 mr-2" />
                  Attachments (Optional)
                </CardTitle>
                <CardDescription className="text-retro-green/70">
                  Upload screenshots, logs, or documents to help us understand
                  your issue
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFilesUploaded={handleFilesUploaded}
                  maxFiles={5}
                  maxFileSize={10}
                />
              </CardContent>
            </Card>

            {/* Step 4: System Information */}
            <SystemInfoCollector
              onSystemInfoChange={handleSystemInfoChange}
              autoCollect={true}
            />

            {/* Submit Button */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                disabled={loading || !validateForm()}
                className="flex-1 bg-retro-green text-black hover:bg-retro-green/90 font-mono"
              >
                {loading ? (
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    PROCESSING...
                  </div>
                ) : (
                  `CREATE TICKET - ${getPriceForTier(pricingTier)}`
                )}
              </Button>
            </div>
          </form>
        )}
      </div>
    </TerminalWindow>
  );
};

export default CreateTicketForm;
