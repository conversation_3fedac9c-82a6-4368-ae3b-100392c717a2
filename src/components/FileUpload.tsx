import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  X, 
  File, 
  Image, 
  FileText, 
  Archive,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadProps {
  onFilesUploaded: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedFileTypes?: string[];
  ticketId?: string;
  className?: string;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadProgress?: number;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 5,
  maxFileSize = 10, // 10MB default
  acceptedFileTypes = [
    'image/*',
    'application/pdf',
    'text/*',
    '.doc,.docx,.xls,.xlsx,.ppt,.pptx',
    '.zip,.rar,.7z'
  ],
  ticketId,
  className
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (fileType.includes('pdf') || fileType.includes('document')) return <FileText className="w-4 h-4" />;
    if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('archive')) return <Archive className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }

    // Check file type
    const isValidType = acceptedFileTypes.some(type => {
      if (type.includes('*')) {
        const baseType = type.split('/')[0];
        return file.type.startsWith(baseType);
      }
      return file.type === type || file.name.toLowerCase().endsWith(type.toLowerCase());
    });

    if (!isValidType) {
      return 'File type not supported';
    }

    return null;
  };

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const fileName = `${fileId}-${file.name}`;
    const filePath = `ticket-attachments/${user?.id}/${fileName}`;

    // Create initial file object
    const uploadedFile: UploadedFile = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      url: '',
      uploadProgress: 0
    };

    try {
      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('attachments')
        .getPublicUrl(filePath);

      uploadedFile.url = publicUrl;
      uploadedFile.uploadProgress = 100;

      // If ticketId is provided, save to database
      if (ticketId) {
        const { error: dbError } = await supabase
          .from('ticket_attachments')
          .insert({
            ticket_id: ticketId,
            file_name: file.name,
            file_size: file.size,
            file_type: file.type,
            file_url: publicUrl,
            storage_path: filePath,
            uploaded_by: user?.id
          });

        if (dbError) {
          console.error('Error saving attachment to database:', dbError);
          // Don't throw here as the file is already uploaded
        }
      }

      return uploadedFile;
    } catch (error: any) {
      uploadedFile.error = error.message || 'Upload failed';
      throw error;
    }
  };

  const handleFileUpload = async (files: File[]) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to upload files.",
        variant: "destructive",
      });
      return;
    }

    if (uploadedFiles.length + files.length > maxFiles) {
      toast({
        title: "Too Many Files",
        description: `Maximum ${maxFiles} files allowed.`,
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    const newFiles: UploadedFile[] = [];

    for (const file of files) {
      const validationError = validateFile(file);
      if (validationError) {
        toast({
          title: "Invalid File",
          description: `${file.name}: ${validationError}`,
          variant: "destructive",
        });
        continue;
      }

      try {
        const uploadedFile = await uploadFile(file);
        newFiles.push(uploadedFile);
      } catch (error: any) {
        toast({
          title: "Upload Failed",
          description: `Failed to upload ${file.name}: ${error.message}`,
          variant: "destructive",
        });
      }
    }

    const allFiles = [...uploadedFiles, ...newFiles];
    setUploadedFiles(allFiles);
    onFilesUploaded(allFiles);
    setUploading(false);
  };

  const removeFile = async (fileId: string) => {
    const fileToRemove = uploadedFiles.find(f => f.id === fileId);
    if (!fileToRemove) return;

    try {
      // Remove from storage if URL exists
      if (fileToRemove.url) {
        const pathMatch = fileToRemove.url.match(/ticket-attachments\/.*$/);
        if (pathMatch) {
          await supabase.storage
            .from('attachments')
            .remove([pathMatch[0]]);
        }
      }

      // Remove from database if ticketId exists
      if (ticketId) {
        await supabase
          .from('ticket_attachments')
          .delete()
          .eq('ticket_id', ticketId)
          .eq('file_name', fileToRemove.name);
      }

      const updatedFiles = uploadedFiles.filter(f => f.id !== fileId);
      setUploadedFiles(updatedFiles);
      onFilesUploaded(updatedFiles);
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to remove file: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    handleFileUpload(acceptedFiles);
  }, [uploadedFiles, maxFiles, user]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles: maxFiles - uploadedFiles.length,
    disabled: uploading || uploadedFiles.length >= maxFiles
  });

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
          isDragActive 
            ? "border-retro-green bg-retro-green/10" 
            : "border-retro-green/50 hover:border-retro-green hover:bg-retro-green/5",
          (uploading || uploadedFiles.length >= maxFiles) && "opacity-50 cursor-not-allowed"
        )}
      >
        <input {...getInputProps()} ref={fileInputRef} />
        <Upload className="w-8 h-8 mx-auto mb-2 text-retro-green" />
        <p className="text-retro-green font-mono">
          {isDragActive
            ? "Drop files here..."
            : `Drag & drop files here, or click to select`}
        </p>
        <p className="text-retro-green/60 text-sm mt-1">
          Max {maxFiles} files, {maxFileSize}MB each
        </p>
        <p className="text-retro-green/60 text-xs mt-1">
          Supported: Images, PDFs, Documents, Archives
        </p>
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-retro-green font-mono font-bold">Attached Files:</h4>
          {uploadedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-3 border border-retro-green/50 rounded bg-black/50"
            >
              <div className="flex items-center space-x-3 flex-1">
                {getFileIcon(file.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-retro-green font-mono text-sm truncate">
                    {file.name}
                  </p>
                  <p className="text-retro-green/60 text-xs">
                    {formatFileSize(file.size)}
                  </p>
                </div>
                {file.error ? (
                  <Badge variant="destructive" className="text-xs">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Error
                  </Badge>
                ) : file.uploadProgress === 100 ? (
                  <Badge className="bg-retro-green text-black text-xs">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Uploaded
                  </Badge>
                ) : (
                  <div className="w-16">
                    <Progress value={file.uploadProgress || 0} className="h-2" />
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(file.id)}
                className="text-retro-green hover:text-red-400 ml-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
