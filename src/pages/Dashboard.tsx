
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { supabase } from '@/integrations/supabase/client';
import ScanlineEffect from '@/components/ScanlineEffect';
import UserMenu from '@/components/UserMenu';
import TerminalWindow from '@/components/TerminalWindow';
import { Badge } from '@/components/ui/badge';
import { BarChart3, Users, Clock, CheckCircle } from 'lucide-react';

interface DashboardStats {
  totalTickets: number;
  pendingTickets: number;
  resolvedTickets: number;
  totalRevenue: number;
}

const Dashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    pendingTickets: 0,
    resolvedTickets: 0,
    totalRevenue: 0
  });
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { isAdmin } = useUserRole();

  useEffect(() => {
    const loadStats = async () => {
      if (!user) return;

      try {
        if (isAdmin) {
          // Admin stats - all paid tickets
          const { data: tickets } = await supabase
            .from('tickets')
            .select('status, payment_status')
            .eq('payment_status', 'paid');

          const { data: payments } = await supabase
            .from('payments')
            .select('amount')
            .eq('status', 'paid');

          const totalTickets = tickets?.length || 0;
          const pendingTickets = tickets?.filter(t => 
            t.status === 'paid' || t.status === 'in_progress'
          ).length || 0;
          const resolvedTickets = tickets?.filter(t => 
            t.status === 'resolved' || t.status === 'closed'
          ).length || 0;
          const totalRevenue = payments?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0;

          setStats({
            totalTickets,
            pendingTickets,
            resolvedTickets,
            totalRevenue
          });
        } else {
          // User stats - their tickets only
          const { data: tickets } = await supabase
            .from('tickets')
            .select('status, payment_status')
            .eq('user_id', user.id);

          const totalTickets = tickets?.length || 0;
          const pendingTickets = tickets?.filter(t => 
            t.status === 'pending_payment' || t.status === 'paid' || t.status === 'in_progress'
          ).length || 0;
          const resolvedTickets = tickets?.filter(t => 
            t.status === 'resolved' || t.status === 'closed'
          ).length || 0;

          setStats({
            totalTickets,
            pendingTickets,
            resolvedTickets,
            totalRevenue: 0 // Users don't see revenue
          });
        }
      } catch (error) {
        console.error('Error loading stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [user, isAdmin]);

  const StatCard = ({ title, value, icon: Icon, color }: {
    title: string;
    value: string | number;
    icon: React.ComponentType<any>;
    color: string;
  }) => (
    <div className="border border-retro-green p-6 bg-retro-terminal/20 hover:bg-retro-terminal/40 transition-colors">
      <div className="flex items-center justify-between">
        <div>
          <div className="text-sm text-retro-green/80 mb-1">{title}</div>
          <div className={`text-2xl font-bold ${color}`}>{value}</div>
        </div>
        <Icon className={`w-8 h-8 ${color}`} />
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />
      
      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">
            {isAdmin ? 'ADMIN_DASHBOARD.EXE' : 'USER_DASHBOARD.EXE'}
          </div>
          <UserMenu />
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">
            &gt; {isAdmin ? 'SYSTEM OVERVIEW' : 'MY ACCOUNT'}
          </h1>
          <p className="text-retro-green/80">
            {isAdmin 
              ? 'Monitor helpdesk performance and statistics'
              : 'Track your support ticket activity'
            }
          </p>
        </div>

        {/* Stats Grid */}
        <TerminalWindow title="STATISTICS.DB">
          {loading ? (
            <div className="text-center text-retro-green">Loading statistics...</div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                title="TOTAL TICKETS"
                value={stats.totalTickets}
                icon={BarChart3}
                color="text-retro-green"
              />
              <StatCard
                title={isAdmin ? "PENDING TICKETS" : "ACTIVE TICKETS"}
                value={stats.pendingTickets}
                icon={Clock}
                color="text-yellow-500"
              />
              <StatCard
                title="RESOLVED TICKETS"
                value={stats.resolvedTickets}
                icon={CheckCircle}
                color="text-green-500"
              />
              {isAdmin && (
                <StatCard
                  title="TOTAL REVENUE"
                  value={`$${(stats.totalRevenue / 100).toFixed(2)}`}
                  icon={Users}
                  color="text-retro-green"
                />
              )}
            </div>
          )}
        </TerminalWindow>

        {/* Quick Actions */}
        <TerminalWindow title="QUICK_ACTIONS.BAT">
          <div className="space-y-4">
            <div className="text-lg font-bold text-retro-green mb-4">
              &gt; AVAILABLE COMMANDS
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-retro-green">▶</span>
                  <span className="font-mono">/tickets</span>
                </div>
                <div className="text-sm text-retro-green/80">
                  {isAdmin ? 'View and manage all support tickets' : 'Create and manage your tickets'}
                </div>
              </div>
              
              {isAdmin && (
                <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-retro-green">▶</span>
                    <span className="font-mono">/admin</span>
                  </div>
                  <div className="text-sm text-retro-green/80">
                    Access advanced admin tools and analytics
                  </div>
                </div>
              )}
            </div>
          </div>
        </TerminalWindow>

        {/* System Status */}
        <TerminalWindow title="SYSTEM_STATUS.LOG">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-retro-green/80">Database Connection:</span>
              <Badge className="bg-green-500 text-black">ONLINE</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-retro-green/80">Payment Gateway:</span>
              <Badge className="bg-green-500 text-black">OPERATIONAL</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-retro-green/80">Support System:</span>
              <Badge className="bg-green-500 text-black">ACTIVE</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-retro-green/80">User Role:</span>
              <Badge className="bg-retro-green text-black">
                {isAdmin ? 'ADMINISTRATOR' : 'USER'}
              </Badge>
            </div>
          </div>
        </TerminalWindow>
      </div>
    </div>
  );
};

export default Dashboard;
